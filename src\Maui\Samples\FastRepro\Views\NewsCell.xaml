<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaDynamicDrawnCell
    x:Class="Sandbox.Views.NewsCell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    BackgroundColor="White"
    Margin="0,4"
    Padding="16">
    
    <draw:SkiaLayout Type="Column" Spacing="12">
        
        <!-- Author Header -->
        <draw:SkiaLayout Type="Row" Spacing="8" HorizontalOptions="Fill">
            <draw:SkiaShape
                x:Name="AvatarFrame"
                Type="Circle"
                WidthRequest="40"
                HeightRequest="40"
                BackgroundColor="LightGray" />
                
            <draw:SkiaLayout Type="Column" HorizontalOptions="Fill">
                <draw:SkiaLabel
                    x:Name="AuthorLabel"
                    FontSize="14"
                    FontWeight="Bold"
                    TextColor="Black" />
                <draw:SkiaLabel
                    x:Name="TimeLabel"
                    FontSize="12"
                    TextColor="Gray" />
            </draw:SkiaLayout>
        </draw:SkiaLayout>
        
        <!-- Content Title -->
        <draw:SkiaLabel
            x:Name="TitleLabel"
            FontSize="16"
            FontWeight="Bold"
            TextColor="Black"
            IsVisible="False" />
            
        <!-- Text Content -->
        <draw:SkiaLabel
            x:Name="ContentLabel"
            FontSize="14"
            TextColor="#333"
            LineBreakMode="WordWrap"
            IsVisible="False" />
            
        <!-- Image Content -->
        <draw:SkiaImage
            x:Name="ContentImage"
            CornerRadius="8"
            Aspect="AspectFill"
            HeightRequest="200"
            IsVisible="False" />
            
        <!-- Video Thumbnail with Play Button -->
        <draw:SkiaLayout
            x:Name="VideoLayout"
            Type="Absolute"
            HeightRequest="200"
            IsVisible="False">
            
            <draw:SkiaImage
                x:Name="VideoThumbnail"
                CornerRadius="8"
                Aspect="AspectFill"
                HorizontalOptions="Fill"
                VerticalOptions="Fill" />
                
            <draw:SkiaShape
                Type="Circle"
                WidthRequest="60"
                HeightRequest="60"
                BackgroundColor="Black"
                Opacity="0.7"
                HorizontalOptions="Center"
                VerticalOptions="Center">
                <draw:SkiaShape.Shadow>
                    <draw:SkiaShadow Color="Black" BlurRadius="10" />
                </draw:SkiaShape.Shadow>
            </draw:SkiaShape>
            
            <draw:SkiaLabel
                Text="▶"
                FontSize="24"
                TextColor="White"
                HorizontalOptions="Center"
                VerticalOptions="Center" />
        </draw:SkiaLayout>
        
        <!-- Article Preview -->
        <draw:SkiaLayout
            x:Name="ArticleLayout"
            Type="Row"
            Spacing="12"
            IsVisible="False">
            
            <draw:SkiaImage
                x:Name="ArticleThumbnail"
                WidthRequest="80"
                HeightRequest="80"
                CornerRadius="4"
                Aspect="AspectFill" />
                
            <draw:SkiaLayout Type="Column" HorizontalOptions="Fill">
                <draw:SkiaLabel
                    x:Name="ArticleTitle"
                    FontSize="14"
                    FontWeight="Bold"
                    TextColor="Black"
                    LineBreakMode="TailTruncation"
                    MaxLines="2" />
                <draw:SkiaLabel
                    x:Name="ArticleDescription"
                    FontSize="12"
                    TextColor="Gray"
                    LineBreakMode="TailTruncation"
                    MaxLines="3" />
            </draw:SkiaLayout>
        </draw:SkiaLayout>
        
        <!-- Ad Content -->
        <draw:SkiaLayout
            x:Name="AdLayout"
            Type="Column"
            Spacing="8"
            IsVisible="False">
            
            <draw:SkiaLabel
                Text="Sponsored"
                FontSize="10"
                TextColor="Gray"
                HorizontalOptions="End" />
                
            <draw:SkiaImage
                x:Name="AdImage"
                CornerRadius="8"
                Aspect="AspectFill"
                HeightRequest="150" />
                
            <draw:SkiaLabel
                x:Name="AdTitle"
                FontSize="14"
                FontWeight="Bold"
                TextColor="Black" />
        </draw:SkiaLayout>
        
        <!-- Interaction Bar -->
        <draw:SkiaLayout Type="Row" Spacing="16" HorizontalOptions="Fill">
            <draw:SkiaButton
                x:Name="LikeButton"
                Text="👍"
                BackgroundColor="Transparent"
                TextColor="Gray"
                FontSize="14" />
                
            <draw:SkiaButton
                x:Name="CommentButton"
                Text="💬"
                BackgroundColor="Transparent"
                TextColor="Gray"
                FontSize="14" />
                
            <draw:SkiaButton
                x:Name="ShareButton"
                Text="📤"
                BackgroundColor="Transparent"
                TextColor="Gray"
                FontSize="14"
                HorizontalOptions="End" />
        </draw:SkiaLayout>
        
    </draw:SkiaLayout>
</draw:SkiaDynamicDrawnCell>
